'use client';

import { Dialog, DialogContent, <PERSON>po<PERSON>, Button, Box } from '@mui/material';
import { usePostDialogStore } from '@minicardiac-client/apis';

interface StatusDialogProps {
  open: boolean;
  onClose: () => void;
  variant: 'thank-you' | 'prestige-note' | 'submitted';
}

const StatusDialog: React.FC<StatusDialogProps> = ({
  open,
  onClose,
  variant,
}) => {
  const { setActiveDialog } = usePostDialogStore();

  const handleClose = () => {
    setActiveDialog(null);
    onClose();
  };

  const getContent = () => {
    switch (variant) {
      case 'thank-you':
        return {
          title: 'Thank you for uploading your documents.',
          content:
            'Our team will review them shortly, and once approved, your account will be reactivated. You can expect full access to be restored within the week.',
          contentColor: '#737678',
          buttonText: 'Okay!',
          maxWidth: '581px',
        };
      case 'prestige-note':
        return {
          title: 'A little note about Prestige before you continue',
          content: [
            'At MiniCardiac, we are committed to upholding the highest standards of medical excellence in the cardiac health industry.',
            'As a Prestige member, your profile is given priority in searches and the news feed, helping you stand out in the eyes of patients, colleagues, and organisations.',
            'To maintain this level of distinction, we ask that all Prestige members uphold a patient rating of at least four stars or higher. While ratings are optional for other subscription tiers, they are an integral part of the Prestige experience.',
            'This ensures that our most highly promoted members consistently reflect the trust and quality that MiniCardiac is known for, maintaining the reputation - and thereby, the value - of the Prestige programme.',
            'Keeping your rating at four stars or above ensures your continued eligibility for Prestige.',
          ],
          contentColor: '#4B4B4B',
          buttonText: 'Got it!',
          maxWidth: '728px',
        };
      case 'submitted':
        return {
          title: 'Application Submitted',
          content:
            'Your application has been submitted successfully! You’ll hear from us soon.',
          contentColor: '#4B4B4B',
          buttonText: 'Okay!',
          maxWidth: '581px',
        };
      default:
        return null;
    }
  };

  const contentData = getContent();
  if (!contentData) return null;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: contentData.maxWidth,
          padding: '20px',
          borderRadius: '12px',
          backgroundColor: '#fff',
        },
      }}
    >
      <DialogContent sx={{ padding: 0 }}>
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center',
            gap: '20px',
          }}
        >
          <Typography fontWeight={500} fontSize="24px" color="#1E1E1E">
            {contentData.title}
          </Typography>

          {Array.isArray(contentData.content) ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
              {contentData.content.map((line, idx) => (
                <Typography
                  key={idx}
                  fontWeight={400}
                  fontSize="16px"
                  color={contentData.contentColor}
                >
                  {line}
                </Typography>
              ))}
            </Box>
          ) : (
            <Typography
              fontWeight={400}
              fontSize="16px"
              color={contentData.contentColor}
            >
              {contentData.content}
            </Typography>
          )}

          {variant !== 'thank-you' && (
            <Button
              onClick={handleClose}
              variant="contained"
              sx={{
                width: '156px',
                height: '40px',
                fontWeight: 700,
                backgroundColor: '#A24295',
                color: '#fff',
                '&:hover': {
                  backgroundColor: '#932080',
                },
                marginTop: '12px',
              }}
            >
              {contentData.buttonText}
            </Button>
          )}
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default StatusDialog;
