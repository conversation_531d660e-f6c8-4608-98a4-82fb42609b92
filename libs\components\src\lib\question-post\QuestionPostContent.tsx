'use client';
import { Box } from '@mui/material';
import { useRouter } from 'next/navigation';
import { FullPageLoader } from '../full-page-loader';
import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { generateSlug } from '@minicardiac-client/utilities';

const QuestionPostContent = ({
  html,
  questionId,
  questionTitle,
  tags,
}: {
  html: string;
  questionId: string;
  questionTitle: string;
  tags: string[];
}) => {
  const router = useRouter();
  const t = useTranslations('questionPost');
  const [loading, setLoading] = useState(false);

  const handleClick = () => {
    const selection = window.getSelection();
    const isTextSelected = selection && selection.toString().length > 0;

    if (isTextSelected) return; // Don't navigate if text is selected

    const slug = generateSlug(questionTitle);
    setLoading(true);

    router.push(`/feed/question/${questionId}/${slug}`);
  };

  return (
    <>
      <FullPageLoader
        open={loading}
        message={t('loadingQuestion')}
        sx={{ backgroundColor: '#1E1E1E40' }}
      />
      <Box
        onClick={handleClick}
        sx={{
          width: '100%',
          color: '#000',
          fontSize: '14px',
          cursor: 'pointer',
          transition: 'background 0.2s ease',
          '& h2': { margin: 0, fontSize: '16px', fontWeight: 700 },
          '& p': { margin: 0, fontSize: '12px', fontWeight: 400 },
        }}
        dangerouslySetInnerHTML={{ __html: html }}
      />
    </>
  );
};

export default QuestionPostContent;
