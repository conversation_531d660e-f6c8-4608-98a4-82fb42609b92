// Request types
export interface RegisterUserRequest {
  email: string;
  password: string;
  displayName?: string;
  accountType?: string;
  token?: string;
}

export interface SessionLoginRequest {
  idToken: string;
}

export interface VerifyOtpRequest {
  email: string;
  otp: string;
}

export interface RegenerateOtpRequest {
  email: string;
}

// Response types
export interface RegisterUserResponse {
  userId: string;
  email: string;
  requiresOTP: boolean;
}

export interface VerifySessionResponse {
  customToken: string;
}

export interface VerifyOtpResponse {
  customToken: string;
}

export interface RegenerateOtpResponse {
  success: boolean;
}

// User data
export interface UserData {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
  currentStage?: string;
}

// Auth state
export interface AuthState {
  user: UserData | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: AuthError | null;
}

// Error type
export interface AuthError {
  message: string;
  code?: string;
}

export interface UserProfileData {
  id: string;
  displayName: string;
  username: string;
  introductoryStatement: string;
  email: string;
  currentStage: string;
  providerId: string;
  accountType: string;
  profileImageUrl: string;
  profileImageUrlThumbnail: string;
  status: number;
  createdAt: string;
  updatedAt: string;
  permissions: {
    global: any[];
    workspace: Array<{
      id: string;
      workspaceId: string;
      status: number;
      roleName: string;
      roleKey: string;
      permissions: any[];
    }>;
  };
}

export type DecodedToken = {
  uid?: string;
  email?: string;
  currentStage?: string;
  [key: string]: any;
};

export enum CurrentStage {
  SUBSCRIPTION = 'onboarding:subscription',
  PROFILE_SETUP = 'onboarding:profile_setup',
  DOCUMENT_UPLOAD = 'onboarding:document_upload',
  ADDING_NETWORK = 'onboarding:adding_network',
  COMPLETED = 'completed',
}
