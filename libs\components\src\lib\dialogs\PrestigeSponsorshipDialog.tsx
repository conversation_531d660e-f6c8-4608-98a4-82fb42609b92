'use client';

import { usePostDialogStore } from '@minicardiac-client/apis';
import { Dialog, DialogContent, Typography, Button, Box } from '@mui/material';
import { useRouter } from 'next/navigation';

const PrestigeSponsorshipDialog = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) => {
  const { setActiveDialog } = usePostDialogStore();
  const router = useRouter();

  function handleSignUp() {
    setActiveDialog(null);
    router.push('/settings');
  }

  function handleCancel() {
    setActiveDialog(null);
    onClose();
  }

  return (
    <Dialog
      open={open}
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: '728px',
          padding: '20px',
          borderRadius: '12px',
          backgroundColor: '#fff',
        },
      }}
    >
      <DialogContent sx={{ padding: 0 }}>
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            textAlign: 'center',
            gap: '20px',
          }}
        >
          <Typography fontWeight={500} fontSize="24px" color="#1E1E1E">
            Apply for industry sponsorship
          </Typography>

          <Typography fontWeight={400} fontSize="16px" color="#4B4B4B">
            The prestige tier offers some amazing benefits, but we get it - it’s
            also demanding on the pocket.
          </Typography>

          <Typography fontWeight={400} fontSize="16px" color="#4B4B4B">
            This is why we also offer Prestige brand ambassadorships - the
            opportunity to partner with industry leaders as the face of their
            brand, to have your prestige plan sponsored for you.
          </Typography>

          <Typography fontWeight={400} fontSize="16px" color="#4B4B4B">
            If you would like to apply for the chance to be selected for a
            Prestige sponsorship right away, it’s as easy as clicking on “sign
            me up” below. Your name will be added to a list visible only to
            Prestige members who are looking to sponsor others.
          </Typography>

          <Typography fontWeight={400} fontSize="16px" color="#4B4B4B">
            You can then carry on and subscribe as you like, and if any of our
            Prestige members are interested in partnering with you, they’ll get
            in touch with you directly.
          </Typography>

          <Typography fontWeight={400} fontSize="16px" color="#4B4B4B">
            You can also apply or retract your application at any time from your
            Settings once you’ve signed up!
          </Typography>

          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              gap: '16px',
              mt: '12px',
            }}
          >
            <Button
              onClick={handleCancel}
              variant="outlined"
              sx={{
                width: '120px',
                height: '40px',
                fontWeight: 700,
                borderColor: '#A24295',
                color: '#A24295',
                '&:hover': {
                  borderColor: '#932080',
                  color: '#932080',
                },
              }}
            >
              Cancel
            </Button>

            <Button
              onClick={handleSignUp}
              variant="contained"
              sx={{
                width: '140px',
                height: '40px',
                fontWeight: 700,
                backgroundColor: '#A24295',
                color: '#fff',
                '&:hover': {
                  backgroundColor: '#932080',
                },
              }}
            >
              Sign me up!
            </Button>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default PrestigeSponsorshipDialog;
