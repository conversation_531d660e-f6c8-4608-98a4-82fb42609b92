'use client';

import './embla/embla.css';

import { useState, useEffect } from 'react';
import { useAuthStore, usePostDialogStore } from '@minicardiac-client/apis';
import {
  Box,
  Button,
  CircularProgress,
  Typography,
  Snackbar,
  Alert,
  useTheme,
} from '@mui/material';
import { EmblaOptionsType } from 'embla-carousel';
import {
  useGetSubscriptionPlans,
  useCreateSubscription,
} from '@minicardiac-client/apis';
import { useRouter } from 'next/navigation';

import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import RemoveCircleIcon from '@mui/icons-material/RemoveCircle';

import { ExtendedTheme } from '../../auth/types/auth.types';
import EmblaCarousel from './embla/EmblaCarousel';
import { LoadingButton } from '../../loading-button/loading-button';
import SubscriptionPlanCard from './SubscriptionPlanCard';
import StatusDialog from '../../dialogs/StatusDialog';
import PrestigeFormDialog from '../../dialogs/PrestigeFormDialog';

export const SubscriptionList = ({
  userSegment = 'CARDIAC_SPECIALIST',
  onExpandChange,
  billingCycle = 'monthly',
  professionalType,
  onClickSwitchToTableButton,
}: {
  userSegment?: string;
  onExpandChange?: (isExpanded: boolean) => void;
  billingCycle?: 'monthly' | 'yearly';
  professionalType?: string | null;
  onClickSwitchToTableButton: () => void;
}) => {
  const theme = useTheme() as ExtendedTheme;
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState<number | null>(null);
  const [viewingPlan, setViewingPlan] = useState<boolean>(false);
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');

  const [alertSeverity, setAlertSeverity] = useState<'success' | 'error'>(
    'success'
  );
  const [showPrestigeForm, setShowPrestigeForm] = useState(false);
  const [prestigeFormSubmitted, setPrestigeFormSubmitted] = useState(false);
  const [prestigeFormData, setPrestigeFormData] = useState(null);

  const { isProcessing, setProcessing } = useAuthStore();
  const { activeDialog, setActiveDialog } = usePostDialogStore();

  useEffect(() => {
    // Reset processing state when component mounts
    setProcessing(false);
  }, [setProcessing]);

  const {
    data: subscriptionPlans,
    isLoading,
    error,
  } = useGetSubscriptionPlans(userSegment);
  const createSubscriptionMutation = useCreateSubscription();

  const handleSelectPlan = (plan: number) => {
    setSelectedPlan(plan);
  };

  const handleProceedToPayment = async () => {
    if (selectedPlan !== null && subscriptionPlans) {
      const plan = subscriptionPlans[selectedPlan];
      console.log('Proceeding to payment for plan:', plan);

      // Set processing state before API call
      setProcessing(true);

      try {
        console.log('calling');
        // Call the subscription API
        await createSubscriptionMutation.mutateAsync({
          subscriptionPlanId: plan.id,
          isYearly: billingCycle === 'yearly',
          ...(professionalType && {
            professionalCategory: 'CARDIAC_SURGEON',
          }),
          prestigeData: prestigeFormData,
        });

        // Show success message
        setAlertSeverity('success');
        setAlertMessage('Subscription created successfully!');
        setAlertOpen(true);

        // Proceed to feed
        router.push('/feed');

        setActiveDialog('MembershipApplied');
      } catch (error: any) {
        console.error('Error creating subscription:', error);

        // Reset processing state on error
        setProcessing(false);

        // Show error message
        setAlertSeverity('error');
        setAlertMessage(
          error.message || 'Failed to create subscription. Please try again.'
        );
        setAlertOpen(true);
      }
    }
  };

  // Handle alert close
  const handleAlertClose = () => {
    setAlertOpen(false);
  };

  const handleFormSubmit = (data: any) => {
    setPrestigeFormData(data);
    setShowPrestigeForm(false);
    setPrestigeFormSubmitted(true);
    setActiveDialog('PrestigeNote');
  };

  const OPTIONS: EmblaOptionsType = {
    loop: false,
    skipSnaps: true,
    watchDrag: viewingPlan === false,
    ...(viewingPlan === true && selectedPlan !== null
      ? { align: 'center', containScroll: false, startIndex: selectedPlan }
      : { containScroll: 'keepSnaps' }),
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !subscriptionPlans || subscriptionPlans.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h6" color="error">
          Unable to load subscription plans. Please try again later.
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        pt: viewingPlan ? '0px' : '40px',
        pb: '40px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: {
          xs: viewingPlan ? 1.5 : '28px',
          sm: viewingPlan ? 1.5 : '40px',
        },
      }}
    >
      {/* Alert for success/error messages */}
      <Snackbar
        open={alertOpen}
        autoHideDuration={6000}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleAlertClose}
          severity={alertSeverity}
          sx={{ width: '100%' }}
        >
          {alertMessage}
        </Alert>
      </Snackbar>
      {selectedPlan !== null && viewingPlan ? (
        <EmblaCarousel
          subscriptionOptions={subscriptionPlans}
          selectedPlan={selectedPlan}
          onSelectPlan={handleSelectPlan}
          options={OPTIONS}
          isExpanded={viewingPlan}
          billingCycle={billingCycle}
        />
      ) : (
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: {
              xs: '1fr',
              sm: '1fr 1fr',
              md: '1fr 1fr',
            },
            '@media (min-width:1220px)': {
              gridTemplateColumns: 'repeat(4, 1fr)',
            },
            width: { xs: '100%', sm: 'auto' },
            gap: { xs: '20px', sm: '24px' },
            justifyItems: 'center',
            px: { xs: '20px', sm: '0px' },
          }}
        >
          {subscriptionPlans.map((val, index) => (
            <SubscriptionPlanCard
              key={val.id}
              isActive={selectedPlan === index}
              onChoose={() => {
                if (!viewingPlan) handleSelectPlan(index);
              }}
              viewingPlan={viewingPlan}
              option={val}
              billingCycle={billingCycle}
            />
          ))}
        </Box>
      )}

      {viewingPlan && (
        <Box display="flex" gap={2.5}>
          <Box display="flex" gap={0.5}>
            <CheckCircleIcon color="success" fontSize="small" />
            <Typography variant="body2">Full access</Typography>
          </Box>
          <Box display="flex" gap={0.5}>
            <CancelIcon color="error" fontSize="small" />
            <Typography variant="body2">Partial access</Typography>
          </Box>
          <Box display="flex" gap={0.5}>
            <RemoveCircleIcon fontSize="small" sx={{ color: '#F9A825' }} />
            <Typography variant="body2">No access</Typography>
          </Box>
        </Box>
      )}

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
        <Box
          sx={(theme) => ({
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '15px',
            width: '100%',
            px: 2,
            mt: '50px',
            [theme.breakpoints.down('sm')]: {
              position: 'fixed',
              bottom: 0,
              left: 0,
              right: 0,
              backgroundColor: '#fff',
              zIndex: 1000,
              boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.1)',
              py: '20px',
            },
          })}
        >
          {viewingPlan ? (
            selectedPlan === 3 && !prestigeFormSubmitted ? (
              <LoadingButton
                variant="contained"
                onClick={() => setShowPrestigeForm(true)}
                disabled={isProcessing}
                sx={{
                  width: '100%',
                  maxWidth: '400px',
                  minWidth: '250px',
                  height: '48px',
                  backgroundColor: '#A24295',
                  borderRadius: '8px',
                  paddingX: '48px',
                  fontFamily: "'Plus Jakarta Sans', sans-serif",
                  fontWeight: 700,
                  fontSize: '16px',
                  lineHeight: '24px',
                  textAlign: 'center',
                  textTransform: 'none',
                  boxShadow: 'none',
                  '&:hover': {
                    backgroundColor: '#B24E9F',
                  },
                }}
              >
                Apply for a Prestige Membership
              </LoadingButton>
            ) : (
              <LoadingButton
                variant="contained"
                onClick={handleProceedToPayment}
                disabled={isProcessing}
                sx={{
                  width: '100%',
                  maxWidth: '400px',
                  minWidth: '250px',
                  height: '48px',
                  backgroundColor: isProcessing ? '#C78ABF' : '#A24295',
                  borderRadius: '8px',
                  paddingX: '48px',
                  fontFamily: "'Plus Jakarta Sans', sans-serif",
                  fontWeight: 700,
                  fontSize: '16px',
                  lineHeight: '24px',
                  textAlign: 'center',
                  textTransform: 'none',
                  boxShadow: 'none',
                  '&:hover': {
                    backgroundColor: isProcessing ? '#C78ABF' : '#B24E9F',
                  },
                }}
              >
                {isProcessing ? (
                  <>
                    <CircularProgress
                      size={20}
                      color="inherit"
                      sx={{ mr: 1 }}
                    />
                    Processing...
                  </>
                ) : (
                  'Proceed to payment'
                )}
              </LoadingButton>
            )
          ) : (
            <LoadingButton
              fullWidth
              disabled={selectedPlan === null}
              variant="contained"
              onClick={() => {
                setViewingPlan(true);
                if (onExpandChange) {
                  onExpandChange(true);
                }
              }}
              sx={(theme: any) => ({
                width: { xs: '280px', sm: '156px' },
                height: (theme as ExtendedTheme).customValues.button?.height,
                gap: (theme as ExtendedTheme).customValues.button?.spacing,
                backgroundColor: selectedPlan !== null && '#A24295',
                color: 'white',
                borderRadius: '4px',
                '&:hover': {
                  backgroundColor: (theme.palette as any).secondary.dark,
                },
              })}
            >
              View Plan
            </LoadingButton>
          )}
        </Box>
        <Box sx={{ mt: '15px' }}>
          {!viewingPlan && (
            <Button onClick={onClickSwitchToTableButton}>
              {/* Text for small screens */}
              <Typography
                sx={{
                  display: { xs: 'block', md: 'none' },
                  marginLeft: '4px',
                  fontSize: { xs: '12px', sm: '16px' },
                  fontWeight: 600,
                  lineHeight:
                    theme.customValues.typography?.button?.small?.lineHeight ||
                    '22px',
                  letterSpacing:
                    theme.customValues.typography?.button?.small
                      ?.letterSpacing || '0px',
                  color: theme.palette.secondary.main,
                }}
              >
                Want the prestige subscription without the price tag? →
              </Typography>

              {/* Original text for medium and up */}
              <Typography
                sx={{
                  display: { xs: 'none', md: 'block' },
                  marginLeft: '4px',
                  fontSize: theme.typography.pxToRem(16),
                  fontWeight: 600,
                  lineHeight:
                    theme.customValues.typography?.button?.small?.lineHeight ||
                    '22px',
                  letterSpacing:
                    theme.customValues.typography?.button?.small
                      ?.letterSpacing || '0px',
                  color: theme.palette.secondary.main,
                }}
              >
                Alternatively, compare all plans in detail →
              </Typography>
            </Button>
          )}
        </Box>
      </Box>

      {showPrestigeForm && (
        <PrestigeFormDialog
          open={showPrestigeForm}
          onClose={() => setShowPrestigeForm(false)}
          onSubmit={handleFormSubmit}
        />
      )}

      {activeDialog === 'PrestigeNote' && (
        <StatusDialog
          onClose={() => setActiveDialog(null)}
          open
          variant="prestige-note"
        />
      )}
    </Box>
  );
};

export default SubscriptionList;
