import {
  Box,
  Typography,
  IconButton,
  Card,
  CardContent,
  CardMedia,
  Button,
} from '@mui/material';
import { Iconify } from '../iconify';
import { FeedPostType } from '@minicardiac-client/types';

interface ScheduledPostCardProps {
  post: FeedPostType;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onReschedule: (id: string) => void;
}

export function ScheduledPostCard({
  post,
  onEdit,
  onDelete,
  onReschedule,
}: ScheduledPostCardProps) {
  const renderContentPreview = () => {
    switch (post.postType) {
      case 'media':
        return (
          <Box display="flex" gap={2} alignItems="flex-start">
            {post.postMedias[0] && (
              <CardMedia
                component="img"
                sx={{
                  width: 64,
                  height: 64,
                  borderRadius: 1,
                  objectFit: 'cover',
                }}
                image={post.postMedias[0].mediaPath}
              />
            )}
            <Typography variant="body2" noWrap>
              {post.content || 'No caption added.'}
            </Typography>
          </Box>
        );
      case 'article':
        return (
          <>
            <Typography variant="body2" fontWeight={600}>
              {post.title}
            </Typography>
            <Typography variant="body2" noWrap>
              {post.content}
            </Typography>
          </>
        );
      case 'poll':
        return (
          <>
            <Typography variant="body2" fontWeight={600}>
              {post.question}
            </Typography>
            <Typography variant="caption">
              Options:{' '}
              {post.options.map((o, i) => (
                <span key={o.id}>
                  <strong>{i + 1}.</strong> {o.text}{' '}
                </span>
              ))}
            </Typography>
          </>
        );
      case 'question':
        return (
          <Typography variant="body2" fontWeight={600}>
            {post.question}
          </Typography>
        );
      default:
        return <Typography variant="body2">{post.content}</Typography>;
    }
  };

  return (
    <Card variant="outlined" sx={{ borderRadius: 1, my: 2, border: 'none' }}>
      <CardContent>
        <Box display="flex" alignItems="center">
          {/* Content Section */}
          <Box width="75%" pr={2} overflow="hidden">
            <Typography
              variant="caption"
              color="text.secondary"
              textTransform="capitalize"
            >
              {post.postType}
            </Typography>
            <Box sx={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
              {renderContentPreview()}
            </Box>

            {/* Scheduled time and reschedule */}
            <Box mt={1} display="flex" alignItems="center" gap={1}>
              <Typography variant="caption">
                {(() => {
                  const dateObj = new Date(post.postScheduleDate as string);
                  const formattedDate = dateObj.toLocaleDateString(undefined, {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                  });
                  const formattedTime = dateObj.toLocaleTimeString(undefined, {
                    hour: '2-digit',
                    minute: '2-digit',
                  });
                  return `Post will go live on ${formattedDate} at ${formattedTime}`;
                })()}
              </Typography>
              <Button
                variant="text"
                size="small"
                onClick={() => onReschedule(post.id)}
                sx={{ textTransform: 'none', minWidth: 0, p: 0, mx: 1 }}
              >
                Reschedule
              </Button>
            </Box>
          </Box>

          {/* Actions Section */}
          <Box
            width="25%"
            display="flex"
            justifyContent="flex-end"
            alignItems="center"
            gap={1}
          >
            <IconButton size="small" onClick={() => onEdit(post.id)}>
              <Iconify
                icon="solar:pen-bold"
                width={20}
                height={20}
                color="#A24295"
              />
            </IconButton>
            <IconButton size="small" onClick={() => onDelete(post.id)}>
              <Iconify
                icon="solar:trash-bin-minimalistic-bold"
                width={20}
                height={20}
                color="#A24295"
              />
            </IconButton>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}
