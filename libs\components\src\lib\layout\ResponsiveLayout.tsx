'use client';
import { Box, useMediaQuery } from '@mui/material';
import Sidebar from '../navigations/Sidebar';
import BottomNavBar from '../navigations/BottomNavBar';
import { useTheme } from '@emotion/react';
import React, { useEffect, useState } from 'react';
import {
  CurrentStage,
  getOwnProfile,
  useAuth,
  UserProfileData,
} from '@minicardiac-client/apis';
import JoinSignupBanner from './JoinSignupBanner';
import ActivePostDialogRenderer from './ActivePostDialogRenderer';
import VerificationBanner from '../search-bar/VerificationBanner';

type ResponsiveLayoutProps = {
  topBar?: React.ReactNode;
  children: React.ReactNode;
  rightSidebar?: React.ReactNode;
  handlePostCreated?: () => void;
};

function ResponsiveLayout({
  topBar,
  children,
  rightSidebar,
  handlePostCreated,
}: ResponsiveLayoutProps) {
  const theme: any = useTheme();
  const { authState } = useAuth();
  const [userDetails, setUserDetails] = useState<UserProfileData | null>(null);

  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    const fetchUser = async () => {
      const data = await getOwnProfile();
      setUserDetails(data);
    };

    fetchUser();
  }, [authState]);

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          height: '100vh',
          width: '100%',
          justifyContent: 'center',
          backgroundColor: '#F3F4F6',
          position: 'relative',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            width: { xs: '100%', lg: '1280px' },
          }}
        >
          {/* Sidebar */}
          {!isMobile && (
            <Box
              sx={{
                height: '100vh',
                overflowY: 'auto',
                backgroundColor: 'white',
                width: { sm: 100, xs: 100, md: 224 },
                minWidth: { sm: 100, xs: 100, md: 224 },
                scrollbarWidth: 'none',
                '&::-webkit-scrollbar': {
                  display: 'none',
                },
              }}
            >
              <Sidebar />
            </Box>
          )}

          <Box
            sx={{
              display: 'flex',
              flex: 1,
              flexDirection: 'row',
              gap: '40px',
              width: '100%',
              bgcolor: '#F3F4F6',
              position: 'relative',
              ml: { xs: '0px', sm: '20px', md: '20px', lg: '40px' },
            }}
          >
            {userDetails &&
              userDetails.currentStage !== CurrentStage.COMPLETED && (
                <VerificationBanner
                  variant={
                    userDetails?.currentStage === CurrentStage.DOCUMENT_UPLOAD
                      ? 'upload-documents'
                      : 'membership-subscribe'
                  }
                  createdAt={'2025-08-04T22:05:00.000Z'}
                />
              )}

            {/* Main Section */}
            <Box
              sx={{
                display: 'flex',
                flex: 1,
                flexDirection: 'column',
                overflow: 'hidden',
                gap: '20px',
                position: 'relative',
              }}
            >
              {/* Top Bars */}
              <Box
                sx={{
                  position: 'fixed',
                  top: 0,
                  zIndex: 100,
                  width: '100%',
                  maxWidth: '719px',
                  backgroundColor: '#F3F4F6',
                  pr: { sm: '140px', md: '244px', lg: '0px' },
                  mt:
                    userDetails?.currentStage !== CurrentStage.COMPLETED
                      ? '60px'
                      : '40px',
                }}
              >
                {topBar}
              </Box>

              {/* Scrollable Main Content */}
              <Box
                sx={{
                  overflowY: 'auto',
                  width: '100%',
                  pr: { sm: '20px', lg: '0px' },
                  scrollbarWidth: 'none',
                  '&::-webkit-scrollbar': {
                    display: 'none',
                  },
                  height: '100vh',
                  mt: '40px',
                }}
              >
                {children}
              </Box>
            </Box>

            {/* Right Sidebar */}
            {!isMobile && (
              <Box
                sx={{
                  mt: '60px',
                }}
              >
                {rightSidebar}
              </Box>
            )}
            {isMobile && (
              <Box
                sx={{
                  position: 'sticky',
                  bottom: 0,
                  zIndex: 10,
                  backgroundColor: 'white',
                }}
              >
                <BottomNavBar />
              </Box>
            )}
          </Box>

          {!authState.isAuthenticated && <JoinSignupBanner />}
        </Box>
      </Box>

      <ActivePostDialogRenderer onPostCreated={handlePostCreated} />
    </>
  );
}

export default React.memo(ResponsiveLayout);
