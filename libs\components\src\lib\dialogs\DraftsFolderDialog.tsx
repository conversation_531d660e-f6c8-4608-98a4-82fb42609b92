// src/components/dialogs/DraftsFolderDialog.tsx
'use client';

import { Dialog, Box, Typography, IconButton } from '@mui/material';
import { Iconify } from '../iconify';
import { sampleDrafts } from './samplePostData';
import { DraftPostCard } from './DraftsFolderCards';

interface DraftsFolderDialogProps {
  open: boolean;
  onClose: () => void;
}

export default function DraftsFolderDialog({
  open,
  onClose,
}: DraftsFolderDialogProps) {
  
  const handleEdit = (id: string) => console.log('Edit', id);
  const handleDelete = (id: string) => console.log('Delete', id);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          borderRadius: '12px',
          backgroundColor: 'white',
          p: 0,
        },
      }}
    >
      {/* Header */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        p={3}
        borderBottom="1px solid #eee"
      >
        <Typography
          variant="h6"
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 600,
            fontSize: '1.5rem',
            color: '#1E1E1E',
          }}
        >
          Drafts
        </Typography>
        <IconButton onClick={onClose}>
          <Iconify
            icon="mingcute:close-line"
            width={36}
            height={36}
            color="#A24295"
          />
        </IconButton>
      </Box>
      <Box p={3}>
        {sampleDrafts.map((post) => (
          <DraftPostCard
            key={post.id}
            post={post}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        ))}
      </Box>
    </Dialog>
  );
}
