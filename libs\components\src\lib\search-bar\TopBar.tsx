'use client';

import { useState, useMemo, useEffect } from 'react';
import { Box, Button } from '@mui/material';
import { useRouter, useSearchParams } from 'next/navigation';

import FilterIcon from '../Icons/FeedIcons/FilterIcon';
import FilterPopup from '../popups/FilterPopup';
import { BackButton } from '../buttons/Backbutton';
import {
  usePostDialogStore,
  useTagsWithFollowState,
} from '@minicardiac-client/apis';
import { SearchBar } from './SearchBar';
import { useTranslations } from 'next-intl';

type TopBarProps = {
  variant?: 'default' | 'tags' | 'filter' | 'saved-posts';
};

export default function TopBar({ variant = 'default' }: TopBarProps) {
  const [openFilter, setOpenFilter] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations('feedNavigation');

  const { setActiveDialog } = usePostDialogStore();

  const { followingTags, followingState, suggestedTags, handleToggleFollow } =
    useTagsWithFollowState({ fetchSuggested: true });

  const tagFilter =
    useMemo(() => searchParams?.get('hashtag'), [searchParams]) || '';
  const folderFilter = useMemo(
    () => searchParams?.get('folder'),
    [searchParams]
  );

  useEffect(() => {
    if (!tagFilter) {
      setIsFollowing(false);
      return;
    }

    const matchedTag = followingTags.find((tag) => tag.tagName === tagFilter);

    if (matchedTag) {
      setIsFollowing(true);
    } else {
      setIsFollowing(false);
    }
  }, [tagFilter, followingTags, followingState]);

  const handleFollowClick = () => {
    if (!tagFilter) return;

    const matchedTag =
      followingTags.find((tag) => tag.tagName === tagFilter) ||
      suggestedTags.find((tag) => tag.tagName === tagFilter);
    if (!matchedTag) return;

    handleToggleFollow(matchedTag.tagId, isFollowing);
    setIsFollowing(!isFollowing);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        maxWidth:
          variant === 'tags' || variant === 'saved-posts'
            ? '976px'
            : { smd: '100%', lg: '719px' },
        width: '100%',
        pr: { sm: '0px', smd: '20px', lg: '0px' },
        gap: '20px',
        position: 'relative',
      }}
    >
      {/* Tags Title */}
      {variant === 'tags' && (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
          <BackButton onClick={() => router.back()} />
          <Box sx={{ fontWeight: 500, fontSize: '20px', color: '#111827' }}>
            {t('tags')}
          </Box>
        </Box>
      )}

      {/* Saved Posts Title + Add Folder */}
      {variant === 'saved-posts' && (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
          <BackButton onClick={() => router.back()} />
          <Box
            sx={{
              fontWeight: 500,
              fontSize: '20px',
              color: '#111827',
              width: '118px',
            }}
          >
            {t('savedPosts')}
          </Box>
          <Button
            variant="contained"
            sx={{
              width: '166px',
              height: '40px',
              fontWeight: 700,
              backgroundColor: '#A24295',
              '&:hover': {
                backgroundColor: '#932080',
              },
            }}
            onClick={() => setActiveDialog('NewFolder')}
          >
            {t('addFolder')}
          </Button>
        </Box>
      )}

      {/* Tag-based or Folder-based Filter Info */}
      {variant === 'filter' && (
        <>
          <BackButton onClick={() => router.back()} />

          <Box
            sx={{
              fontWeight: 700,
              fontSize: '16px',
              color: '#A24295',
              whiteSpace: 'nowrap',
            }}
          >
            {tagFilter ? `#${tagFilter}` : folderFilter}
          </Box>

          {/* Show Follow only for tag filter */}
          {tagFilter && (
            <Button
              variant="outlined"
              onClick={handleFollowClick}
              sx={{
                height: '32px',
                borderRadius: '8px',
                fontSize: '12px',
                fontWeight: 700,
                padding: '5px 12px',
                textTransform: 'none',
                whiteSpace: 'nowrap',
                width: 'fit-content',
                ...(isFollowing
                  ? {
                      backgroundColor: 'white',
                      color: '#A24295',
                      borderColor: '#A24295',
                    }
                  : {
                      backgroundColor: '#A24295',
                      color: 'white',
                      borderColor: '#A24295',
                    }),
                ':hover': {
                  backgroundColor: '#A24295',
                  color: 'white',
                  borderColor: '#A24295',
                },
              }}
            >
              {isFollowing ? t('following') : t('follow')}
            </Button>
          )}
        </>
      )}

      {/* Search Input */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%',
          height: '100%',
        }}
      >
        <SearchBar />
      </Box>

      {/* Filter Icon (only for 'default' and 'filter') */}
      {(variant === 'default' || variant === 'filter') && (
        <Box
          sx={{
            border: '1px solid #A24295',
            borderRadius: '8px',
            backgroundColor: 'white',
            p: '8px',
            cursor: 'pointer',
            position: 'relative',
            ':hover': { backgroundColor: '#F6ECF4' },
          }}
          onClick={() => setOpenFilter(!openFilter)}
        >
          <FilterIcon />
          {openFilter && <FilterPopup onClose={() => setOpenFilter(false)} />}
        </Box>
      )}
    </Box>
  );
}
