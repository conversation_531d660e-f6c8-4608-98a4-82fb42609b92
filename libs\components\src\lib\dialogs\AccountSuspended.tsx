'use client';

import { usePostDialogStore } from '@minicardiac-client/apis';
import { Dialog, DialogContent, Typography, Button, Box } from '@mui/material';
import { useRouter } from 'next/navigation';

const AccountSuspendedDialog = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) => {
  const { setActiveDialog } = usePostDialogStore();
  const router = useRouter();

  function handleUpload() {
    setActiveDialog(null);
    router.push('/professional/document-verification');
  }

  return (
    <Dialog
      open={open}
      // onClose={onClose}
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: '581px',
          height: '202px',
          padding: '20px',
          borderRadius: '12px',
          backgroundColor: '#fff',
        },
      }}
    >
      <DialogContent sx={{ padding: 0 }}>
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'space-between',

            textAlign: 'center',
          }}
        >
          <Typography fontWeight={500} fontSize="24px" color="#1E1E1E">
            Account Suspended
          </Typography>

          <Typography fontWeight={400} fontSize="16px" color="#4B4B4B">
            Your account is currently suspended due to incomplete verification.
            <br />
            Please upload your verification documents to reactivate your
            account.
          </Typography>

          <Button
            onClick={handleUpload}
            variant="contained"
            sx={{
              width: '156px',
              height: '40px',
              fontWeight: 700,
              backgroundColor: '#A24295',
              color: '#fff',
              '&:hover': {
                backgroundColor: '#932080',
              },
            }}
          >
            Upload
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default AccountSuspendedDialog;
