import {
  Box,
  Typography,
  IconButton,
  Card,
  CardContent,
  CardMedia,
} from '@mui/material';
import { Iconify } from '../iconify';
import { FeedPostType } from '@minicardiac-client/types';

interface DraftPostCardProps {
  post: FeedPostType;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}

export function DraftPostCard({ post, onEdit, onDelete }: DraftPostCardProps) {
  const renderContentPreview = () => {
    switch (post.postType) {
      case 'media':
        return (
          <Box display="flex" gap={2} alignItems="flex-start">
            {post.postMedias[0] && (
              <CardMedia
                component="img"
                sx={{
                  width: 64,
                  height: 64,
                  borderRadius: 1,
                  objectFit: 'cover',
                }}
                image={post.postMedias[0].mediaPath}
              />
            )}
            <Typography variant="body2" noWrap>
              {post.content || 'No caption added.'}
            </Typography>
          </Box>
        );
      case 'article':
        return (
          <>
            <Typography variant="body2" fontWeight={600}>
              {post.title}
            </Typography>
            <Typography variant="body2" noWrap>
              {post.content}
            </Typography>
          </>
        );
      case 'poll':
        return (
          <>
            <Typography variant="body2" fontWeight={600}>
              {post.question}
            </Typography>
            <Typography variant="caption">
              Options:{' '}
              {post.options.map((o, i) => (
                <span key={o.id}>
                  <strong>{i + 1}.</strong> {o.text}{' '}
                </span>
              ))}
            </Typography>
          </>
        );
      case 'question':
        return (
          <Typography variant="body2" fontWeight={600}>
            {post.question}
          </Typography>
        );
      default:
        return <Typography variant="body2">{post.content}</Typography>;
    }
  };

  return (
    <Card variant="outlined" sx={{ borderRadius: 1, my: 2, border: 'none' }}>
      <CardContent>
        <Box display="flex" alignItems="center">
          <Box width="75%" pr={2} overflow="hidden">
            <Typography
              variant="caption"
              color="text.secondary"
              textTransform="capitalize"
            >
              {post.postType}
            </Typography>
            <Box sx={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
              {renderContentPreview()}
            </Box>
          </Box>

          <Box
            width="25%"
            display="flex"
            justifyContent="flex-end"
            alignItems="center"
            gap={1}
          >
            <IconButton size="small" onClick={() => onEdit(post.id)}>
              <Iconify icon="solar:pen-bold" width={20} height={20} color="#A24295"/>
            </IconButton>
            <IconButton size="small" onClick={() => onDelete(post.id)}>
              <Iconify icon="solar:trash-bin-minimalistic-bold" width={20} height={20} color="#A24295"/>
            </IconButton>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}
