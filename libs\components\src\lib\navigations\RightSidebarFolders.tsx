'use client';

import { Box, Typography, Button } from '@mui/material';
import { usePostDialogStore } from '@minicardiac-client/apis';
import { useDroppable } from '@dnd-kit/core';
import { useTranslations } from 'next-intl';

const BACKGROUND_IMAGE =
  'url(https://images.unsplash.com/photo-**********-2a8555f1a136?q=80&w=2047&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)';

const folders = [
  'Important Articles',
  'Surgery',
  'New Procedures',
  'Cardiology',
  'Patient Notes',
];

const FolderCard = ({ name }: { name: string }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `folder-${name}`,
  });

  return (
    <Box
      ref={setNodeRef}
      sx={{
        width: isOver ? '200px' : '177px',
        height: isOver ? '80px' : '64px',
        borderRadius: '12px',
        backgroundImage: BACKGROUND_IMAGE,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'box-shadow 0.2s ease, border 0.2s ease',
        boxShadow: isOver ? 6 : 1,
        border: isOver ? '2px solid #A24295' : '2px solid transparent',
        cursor: 'pointer',
      }}
    >
      <Typography color="white" fontSize="16px" fontWeight={700}>
        {name}
      </Typography>
    </Box>
  );
};

const RightSidebarFolders = () => {
  const { setActiveDialog } = usePostDialogStore();
  const t = useTranslations('feedNavigation');

  const hasFolders = folders.length > 0;

  return (
    <Box
      sx={{
        display: { xs: 'none', lg: 'flex' },
        flexDirection: 'column',
      }}
    >
      <Box
        sx={{
          width: 217,

          backgroundColor: 'white',
          borderRadius: '8px',
          mb: '40px',
          height: '687px',
          display: 'flex',
          flexDirection: 'column',
          gap: '40px',
        }}
      >
        <Box sx={{ p: '20px 20px 0' }}>
          <Typography fontWeight={500} fontSize="20px" color="#1E1E1E">
            {t('yourFolders')}
          </Typography>
        </Box>

        <Box
          sx={{
            px: '20px',
            flex: 1,
            display: 'flex',
            justifyContent: hasFolders ? 'flex-start' : 'center',
            alignItems: hasFolders ? 'flex-start' : 'center',
            flexDirection: 'column',
            gap: hasFolders ? '16px' : '20px',
          }}
        >
          {hasFolders ? (
            folders.map((folder) => <FolderCard key={folder} name={folder} />)
          ) : (
            <>
              <Typography
                fontSize="12px"
                fontWeight={400}
                color="#1E1E1E"
                textAlign="center"
              >
                {t('noFolders')} <br />
                {t('clickToGetStarted')}
              </Typography>
              <Button
                variant="contained"
                onClick={() => setActiveDialog('NewFolder')}
                sx={{
                  width: '156px',
                  height: '40px',
                  fontWeight: 700,
                  backgroundColor: '#A24295',
                  '&:hover': {
                    backgroundColor: '#932080',
                  },
                }}
              >
                {t('addFolder')}
              </Button>
            </>
          )}
        </Box>

        {hasFolders && (
          <Box p="16px 20px">
            <Button
              variant="contained"
              onClick={() => setActiveDialog('NewFolder')}
              sx={{
                width: '100%',
                height: '40px',
                fontWeight: 700,
                backgroundColor: '#A24295',
                '&:hover': {
                  backgroundColor: '#932080',
                },
              }}
            >
              {t('addFolder')}
            </Button>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default RightSidebarFolders;
