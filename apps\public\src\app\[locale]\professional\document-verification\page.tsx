'use client';

import React from 'react';
import { Box, Container, Typography } from '@mui/material';
import { useRouter } from '@/apps/public/src/i18n/navigation';
import { usePostDialogStore } from '@minicardiac-client/apis';
import {
  useSnackbar,
  ActionButtonsTemplate,
} from '@minicardiac-client/components';
import DocumentUploadForm from '@/libs/components/src/lib/onboarding/components/Documents/DocumentUpload';

export default function ProfessionalDocumentVerificationPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const { showSuccess, showError } = useSnackbar();
  const { setActiveDialog } = usePostDialogStore();

  const handleDoThisLater = () => {
    router.back();
  };

  const handleContinue = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      // await refreshSession();
      // await uploadDocuments();

      showSuccess('Documents saved successfully!');
      setTimeout(() => {
        router.push('/feed');
        setActiveDialog('DocumentUploaded');
      }, 1000);
    } catch (err: any) {
      console.error('Error saving document data:', err);

      let userFriendlyError =
        'An unexpected error occurred. Please try again later.';

      if (
        err.response?.data?.message?.includes(
          'values() must be called with at least one value'
        )
      ) {
        userFriendlyError = 'Something went wrong. Please try again.';
      } else if (err.response?.status === 403) {
        userFriendlyError =
          'Access denied. Your session may have expired. Please try again.';
      }

      setError(userFriendlyError);
      showError(userFriendlyError);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ p: '40px' }}>
      <Typography fontWeight={500} fontSize={'28px'} textAlign={'center'}>
        Document verification
      </Typography>

      <DocumentUploadForm hideSteppers={true} />

      <ActionButtonsTemplate
        onSave={handleContinue}
        onSkip={handleDoThisLater}
        isSubmitting={isSubmitting}
        isValid={true}
        saveButtonText="Save and Continue"
        skipButtonText="Cancel"
        variant="professional"
      />

      {error && (
        <Box sx={{ color: 'error.main', mt: 2, textAlign: 'center' }}>
          {error}
        </Box>
      )}
    </Container>
  );
}
