'use client';

import { Box, Button, Typography } from '@mui/material';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

const BACKGROUND_IMAGE =
  'url(https://images.unsplash.com/photo-1551601651-2a8555f1a136?q=80&w=2047&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)';

const suggestedTags = [
  { name: '#CuttingEdge', background: BACKGROUND_IMAGE },
  { name: '#Clinics', background: BACKGROUND_IMAGE },
];

const followedTags = [
  { name: '#Surgery', background: BACKGROUND_IMAGE },
  { name: '#Technology', background: BACKGROUND_IMAGE },
  { name: '#Medicine', background: BACKGROUND_IMAGE },
  { name: '#Cardiology', background: BACKGROUND_IMAGE },
  { name: '#Surgery2', background: BACKGROUND_IMAGE },
  { name: '#Technology2', background: BACKGROUND_IMAGE },
];

const TagCard = ({
  name,
  background,
}: {
  name: string;
  background: string;
}) => (
  <Box
    sx={{
      width: '177px',
      height: '64px',
      borderRadius: '8px',
      backgroundImage: background,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    }}
  >
    <Typography color="white" fontSize="16px" fontWeight={700}>
      {name}
    </Typography>
  </Box>
);

const RightSidebarTags = () => {
  const router = useRouter();

  const t = useTranslations('tagsPage');

  return (
    <Box
      sx={{
        display: { xs: 'none', lg: 'flex' },
        flexDirection: 'column',
        position: 'relative',
      }}
    >
      {/* {authState.isAuthenticated && (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            position: 'absolute',
            right: '16px',
            zIndex: 101,
          }}
        >
          <BookmarkIcon fill="white" />
        </Box>
      )} */}

      <Box
        sx={{
          width: 217,
          backgroundColor: 'white',
          borderRadius: '8px',
          mb: '40px',
          maxHeight: 'calc(100vh - 145px)',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Box sx={{ p: '20px 20px 0' }}>
          <Typography fontWeight={500} fontSize="20px" color="#1E1E1E">
            {t('tags')}
          </Typography>
        </Box>

        <Box
          sx={{
            overflowY: 'auto',
            px: '20px',
            flex: 1,
            scrollbarWidth: 'none',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
          }}
        >
          <Typography fontSize="16px" mb="20px">
            {t('suggested')}
          </Typography>
          <Box display="flex" flexDirection="column" gap="16px" mb="40px">
            {suggestedTags.map((tag, i) => (
              <TagCard key={i} {...tag} />
            ))}
          </Box>

          <Typography fontSize="16px" mb="20px">
            {t('followed')}
          </Typography>
          <Box display="flex" flexDirection="column" gap="16px" mb="20px">
            {followedTags.map((tag, i) => (
              <TagCard key={i} {...tag} />
            ))}
          </Box>
        </Box>

        <Box
          sx={{
            boxShadow: '0 -4px 20px rgba(0,0,0,0.1)',
            p: '16px 20px',
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Button
            onClick={() => router.push('/feed/tags')}
            sx={{
              fontSize: '12px',
              fontWeight: 700,
              color: '#A24295',
              textAlign: 'center',
              textTransform: 'none',
              padding: 0,
              minWidth: 'auto',
              background: 'none',
              '&:hover': {
                backgroundColor: 'transparent',
                textDecoration: 'underline',
              },
            }}
          >
            {t('browseAllTags')} →
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default RightSidebarTags;
