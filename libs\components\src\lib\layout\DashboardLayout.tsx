'use client';
import { useEffect, useState, useCallback, useMemo } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useQueryClient } from '@tanstack/react-query';
import WelcomeOverlay from './WelcomeOverlay';
import ResponsiveLayout from './ResponsiveLayout';
import TopBar from '../search-bar/TopBar';
import RightSidebar from '../navigations/RightSidebar';
import TagsMainContent from '../tags/TagsMainContent';
import MainContent from './MainContent';
import ContentTextPost from '../content-posts/ContentTextPost';
import MobileTopBar from '../search-bar/MobileTopBar';
import { MobileSearchbar } from '../navigations/MobileSearchBar';
import { useFeed, useGetOwnProfile } from '@minicardiac-client/apis';
import { useLikePost } from '@minicardiac-client/apis';
// import { FullPageLoader } from '../full-page-loader';
import ContentQuestionPost from '../content-posts/ContentQuestionPost';
import ContentPollPost from '../content-posts/ContentPollPost';
import { ContentArticlePost } from '../content-posts/ContentArticlePost';
import ContentMediaPost from '../content-posts/ContentMediaPost';
import ContentLinkPost from '../content-posts/ContentLinkPost';
import FullFolderSectionView from '../saved-posts/FullFolderSectionView';
import ConditionalDraggablePost from './ConditionalDraggablePost';
import { PostWithNotification } from './PostWithNotification';
import { getCdnUrl, truncate } from '@minicardiac-client/utilities';
import { useFeedStore } from '../store/useFeedStore';
import { useFeedSearchStore } from '../store/useFeedSearchStore';
import Fade from '@mui/material/Fade';
import Box from '@mui/material/Box';
import { PostSkeletonCard } from '../skeleton-ui/PostSkeletonCard';
import {
  FeedPostType,
  FeedSearchState,
  FeedState,
} from '@minicardiac-client/types';
import AddPostFAB from './AddPostFAB';

export default function DashboardLayout() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  // const hasFilter = searchParams?.has('filter');

  // State
  const [showWelcome, setShowWelcome] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const fromSignup = searchParams?.get('fromSignup');
  const hashTagFilter = !!searchParams?.has('hashtag');
  const folderFilter = !!searchParams?.has('folder');

  const searchKeyword = useFeedSearchStore(
    (state: FeedSearchState) => state.searchKeyword
  );
  const postTypes = useFeedSearchStore(
    (state: FeedSearchState) => state.postTypes
  );

  const isTagsPage = pathname?.includes('/feed/tags') ?? false;
  const isSavedPostsPage = pathname?.includes('/feed/saved-posts') ?? false;

  // Search Bar variants
  const topbarVariant = isTagsPage
    ? 'tags'
    : isSavedPostsPage
    ? 'saved-posts'
    : hashTagFilter || folderFilter
    ? 'filter'
    : 'default';

  // Fetch posts feed with refetch function
  const {
    data: feedResponse,
    isLoading,
    // error,
    refetch: refetchFeed,
  } = useFeed({
    postTypes,
    searchKeyword,
    tagName: hashTagFilter ? searchParams?.get('hashtag') || '' : undefined,
  });

  // Get user profile data for filtering
  const { data: userProfile } = useGetOwnProfile();

  // FIXED: Get feed data with proper validation and immediate postType setting
  const feed = useMemo(() => {
    if (!feedResponse) {
      return [];
    }

    let processedFeed: any[] = [];

    if (Array.isArray(feedResponse)) {
      processedFeed = feedResponse.map((post) => ({
        ...post,
        postType: post.postType || 'text',
      }));
    } else if (feedResponse.data && Array.isArray(feedResponse.data)) {
      processedFeed = feedResponse.data.map((post) => ({
        ...post,
        postType: post.postType || 'text', // Set default postType immediately
      }));
    } else {
      return [];
    }

    // Filter out PublicForum posts for Professional and Organization users
    if (
      userProfile?.accountType === 'PROFESSIONAL' ||
      userProfile?.accountType === 'ORGANIZATION'
    ) {
      processedFeed = processedFeed.filter((post) => {
        return !post.tags?.some(
          (tag: string) => tag.toLowerCase() === 'publicforum'
        );
      });
    }

    return processedFeed;
  }, [feedResponse, userProfile?.accountType]);

  // Handle post creation and refresh feed
  const handlePostCreated = useCallback(async () => {
    try {
      // Invalidate the feed query to force a refetch
      await queryClient.invalidateQueries({
        queryKey: ['feed'],
        refetchType: 'active',
        exact: true,
      });

      // Also explicitly refetch as a fallback
      await refetchFeed();
    } catch (error) {
      console.error('Error refreshing feed:', error);
    }
  }, [queryClient, refetchFeed]);

  // Handle welcome screen for new signups
  useEffect(() => {
    if (fromSignup === 'true') {
      setShowWelcome(true);
      window.history.replaceState({}, '', pathname);
    }
  }, [fromSignup, pathname]);

  const handleCloseWelcome = () => setShowWelcome(false);

  const setFeed = useFeedStore((state: FeedState) => state.setFeed);

  useEffect(() => {
    if (feed?.length > 0) {
      setFeed(feed);
    }
  }, [feed, setFeed]);

  useEffect(() => {
    if (!hashTagFilter) return;

    // Delayed when filter changes
    const timeout = setTimeout(() => {
      refetchFeed();
    }, 300);

    return () => clearTimeout(timeout);
  }, [hashTagFilter, refetchFeed]);

  useEffect(() => {
    async function refetchIfNeeded() {
      if (hashTagFilter) {
        await refetchFeed();
      }
    }

    refetchIfNeeded();
  }, [hashTagFilter, refetchFeed]);

  const likeMutation = useLikePost();

  const handleLikeChange = useCallback(
    (postId: string, like: boolean) => {
      useFeedStore.getState().updateLike(postId, like);

      likeMutation.mutate(
        { postId, like },
        {
          onError: (error) => {
            console.error('Failed to like post:', error);
            useFeedStore.getState().updateLike(postId, !like);
          },
        }
      );
    },
    [likeMutation]
  );

  return (
    <>
      <WelcomeOverlay open={showWelcome} onClose={handleCloseWelcome} />

      <ResponsiveLayout
        topBar={
          !showWelcome &&
          (isMobile ? (
            <Box
              sx={{
                bgcolor: '#F3F4F6',
                pb: '5px',
              }}
            >
              <MobileTopBar variant={topbarVariant} />
              <MobileSearchbar
                variant="default"
                filterOpen={filterOpen}
                onFilterOpenChange={setFilterOpen}
              />
            </Box>
          ) : (
            <TopBar variant={topbarVariant} />
          ))
        }
        rightSidebar={
          !isTagsPage && !isSavedPostsPage ? (
            <RightSidebar variant={folderFilter ? 'folders' : 'tags'} />
          ) : undefined
        }
        handlePostCreated={handlePostCreated}
      >
        <Box mt={{ xs: '140px', sm: '100px' }}>
          {isTagsPage ? (
            <TagsMainContent />
          ) : isSavedPostsPage ? (
            <FullFolderSectionView />
          ) : (
            <MainContent>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '16px',
                }}
              >
                {isLoading ? (
                  [...Array(4)].map((_, idx) => <PostSkeletonCard key={idx} />)
                ) : (
                  <Fade in={!isLoading} timeout={400}>
                    <div>
                      {(() => {
                        return feed?.map((post: FeedPostType) => {
                          // FIXED: Added safety check for post existence
                          if (!post || !post.id) {
                            return null;
                          }

                          //Safely Handling title
                          const title =
                            typeof post?.title === 'string'
                              ? post.title
                              : JSON.stringify(post?.title);
                          const truncatedTitle = truncate(title, 150);

                          // FIXED: postType is now guaranteed to be set from useMemo
                          const postType = post.postType; // Will be 'text' if originally undefined

                          if (postType === 'media') {
                            return (
                              <PostWithNotification
                                key={post?.id}
                                tags={post?.tags}
                                postId={post?.id}
                              >
                                <Box sx={{ mb: 3 }}>
                                  <ConditionalDraggablePost
                                    postId={post?.id}
                                    type="media"
                                    folderFilter={folderFilter}
                                  >
                                    <ContentMediaPost
                                      postId={post?.id}
                                      title={truncatedTitle || ''}
                                    />
                                  </ConditionalDraggablePost>
                                </Box>
                              </PostWithNotification>
                            );
                          } else if (postType === 'article') {
                            return (
                              <PostWithNotification
                                key={post?.id}
                                tags={post?.tags}
                                postId={post?.id}
                              >
                                <Box sx={{ mb: 3 }}>
                                  <ConditionalDraggablePost
                                    postId={post?.id}
                                    type="article"
                                    folderFilter={folderFilter}
                                  >
                                    <ContentArticlePost
                                      postId={post?.id}
                                      title={truncatedTitle || ''}
                                      content={post?.content}
                                      summary={post?.content?.slice(0, 200)}
                                      coverImage={
                                        post?.coverImagePath
                                          ? getCdnUrl(post.coverImagePath)
                                          : undefined
                                      }
                                      user={{
                                        name:
                                          post?.publisherName || 'Anonymous',
                                        profilePic:
                                          post?.profileImageUrlThumbnail
                                            ? getCdnUrl(
                                                post?.profileImageUrlThumbnail
                                              )
                                            : '/placeholder-avatar.png',
                                        postedAgo: post?.postedAt
                                          ? new Date(
                                              post?.postedAt
                                            ).toLocaleDateString()
                                          : '',
                                      }}
                                      likes={post?.likesCount}
                                      isLiked={post?.isLiked}
                                      comments={post?.commentsCount}
                                      reposts={post?.repostCount}
                                      shares={post?.shareCount}
                                      tags={post?.tags}
                                      onLikeChange={(isLiked) =>
                                        handleLikeChange(post?.id, isLiked)
                                      }
                                    />
                                  </ConditionalDraggablePost>
                                </Box>
                              </PostWithNotification>
                            );
                          } else if (postType === 'poll') {
                            return (
                              <PostWithNotification
                                key={post?.id}
                                tags={post?.tags}
                                postId={post?.id}
                              >
                                <Box sx={{ mb: 3 }}>
                                  <ConditionalDraggablePost
                                    postId={post?.id}
                                    type="poll"
                                    folderFilter={folderFilter}
                                  >
                                    <ContentPollPost
                                      title={truncatedTitle || ''}
                                      post={post}
                                    />
                                  </ConditionalDraggablePost>
                                </Box>
                              </PostWithNotification>
                            );
                          } else if (postType === 'question') {
                            return (
                              <PostWithNotification
                                key={post?.id}
                                tags={post?.tags}
                                postId={post?.id}
                              >
                                <Box sx={{ mb: 3 }}>
                                  <ConditionalDraggablePost
                                    postId={post?.id}
                                    type="question"
                                    folderFilter={folderFilter}
                                  >
                                    <ContentQuestionPost
                                      title={truncatedTitle || ''}
                                      post={post}
                                    />
                                  </ConditionalDraggablePost>
                                </Box>
                              </PostWithNotification>
                            );
                          } else if (postType === 'link') {
                            return (
                              <PostWithNotification
                                key={post?.id}
                                tags={post?.tags}
                                postId={post?.id}
                              >
                                <Box sx={{ mb: 3 }}>
                                  <ConditionalDraggablePost
                                    postId={post?.id}
                                    type="link"
                                    folderFilter={folderFilter}
                                  >
                                    <ContentLinkPost
                                      postId={post?.id}
                                      linkUrl={post?.linkUrl}
                                      content={post?.content}
                                      linkPreview={post?.linkPreview}
                                      user={{
                                        name:
                                          post?.publisherName || 'Anonymous',
                                        profilePic:
                                          post?.profileImageUrlThumbnail
                                            ? getCdnUrl(
                                                post?.profileImageUrlThumbnail
                                              )
                                            : '/placeholder-avatar.png',
                                        postedAgo: post?.postedAt
                                          ? new Date(
                                              post?.postedAt
                                            ).toLocaleDateString()
                                          : '',
                                      }}
                                      likes={post?.likesCount}
                                      isLiked={post?.isLiked}
                                      comments={post?.commentsCount}
                                      reposts={post?.repostCount}
                                      shares={post?.shareCount}
                                      tags={post?.tags}
                                      onLikeChange={(isLiked) =>
                                        handleLikeChange(post?.id, isLiked)
                                      }
                                    />
                                  </ConditionalDraggablePost>
                                </Box>
                              </PostWithNotification>
                            );
                          }
                          // Default to text post (this handles postType === 'text' and any other values)
                          return (
                            <PostWithNotification
                              key={post?.id}
                              tags={post?.tags}
                              postId={post?.id}
                            >
                              <Box sx={{ mb: 3 }}>
                                <ConditionalDraggablePost
                                  postId={post?.id}
                                  type="text"
                                  folderFilter={folderFilter}
                                >
                                  <ContentTextPost
                                    id={post?.id}
                                    title={truncatedTitle || ''}
                                    user={{
                                      name: post?.publisherName || 'Anonymous',
                                      profilePic: post?.profileImageUrlThumbnail
                                        ? getCdnUrl(
                                            post?.profileImageUrlThumbnail
                                          )
                                        : '/placeholder-avatar.png',
                                      postedAgo: post?.postedAt
                                        ? new Date(
                                            post?.postedAt
                                          ).toLocaleDateString()
                                        : '',
                                    }}
                                    content={post?.content}
                                    likes={post?.likesCount}
                                    isLiked={post?.isLiked}
                                    comments={post?.commentsCount}
                                    reposts={post?.repostCount}
                                    shares={post?.shareCount}
                                    postId={post.id ?? ''}
                                  />
                                </ConditionalDraggablePost>
                              </Box>
                            </PostWithNotification>
                          );
                        });
                      })()}
                    </div>
                  </Fade>
                )}
              </Box>
            </MainContent>
          )}
        </Box>
      </ResponsiveLayout>

      {/* Add Post FAB */}
      <AddPostFAB onPostCreated={handlePostCreated} isFilterOpen={filterOpen} />
    </>
  );
}
