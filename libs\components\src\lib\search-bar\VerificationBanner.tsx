'use client';

import React, { useEffect, useState } from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import { Iconify } from '../iconify';
import { usePostDialogStore } from '@minicardiac-client/apis';
import { useRouter } from 'next/navigation';
import { calculateTimeLeft } from '@minicardiac-client/utilities';

type VerificationBannerVariant = 'upload-documents' | 'membership-subscribe';
type UploadType = 'default' | 'countdown';

interface VerificationBannerProps {
  variant: VerificationBannerVariant;
  uploadType?: UploadType;
  createdAt?: string; // ISO string like "2025-08-18T18:58:34.602Z"
}

const VerificationBanner: React.FC<VerificationBannerProps> = ({
  variant,
  uploadType = 'default',
  createdAt,
}) => {
  const { setActiveDialog } = usePostDialogStore();
  const router = useRouter();

  const [visible, setVisible] = useState(true);
  const [status, setStatus] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  } | null>(null);

  useEffect(() => {
    if (variant !== 'upload-documents' || !createdAt) return;

    const interval = setInterval(() => {
      const result = calculateTimeLeft(createdAt);
      if (!result) return;

      const { status, timeLeft } = result;

      if (status === 'after') {
        clearInterval(interval);
        setActiveDialog('AccountSuspended');
      }

      setTimeLeft(timeLeft);
      setStatus(status);
    }, 1000);

    return () => clearInterval(interval);
  }, [createdAt, setActiveDialog, variant]);

  if (!visible || !createdAt) return null;

  const getMessage = () => {
    if (variant === 'upload-documents') {
      if (status === 'before') {
        return 'Verification pending – Upload your documents to unlock full access.';
      }

      if (status === 'during') {
        if (!timeLeft) return 'Document Verification Pending';

        const { days, hours, minutes } = timeLeft;
        let timeText = '';

        if (days > 0) {
          timeText = `${days} day${days > 1 ? 's' : ''}`;
        } else {
          timeText = `${String(hours).padStart(2, '0')} hours ${String(
            minutes
          ).padStart(2, '0')} minutes`;
        }

        return `Document Verification Pending – Your account will be suspended in ${timeText}`;
      }

      if (status === 'after') {
        return 'Document Verification Pending – Your account has been suspended';
      }
    }

    if (variant === 'membership-subscribe') {
      return (
        <>
          Your profile has been verified!{' '}
          <Box
            component="span"
            fontSize={16}
            fontWeight={700}
            sx={{ color: '#A24295', cursor: 'pointer' }}
            onClick={() => router.push('/professional/subscription')}
          >
            Subscribe
          </Box>{' '}
          to a Membership plan for more benefits.
        </>
      );
    }

    return null;
  };

  const getIcon = () => {
    return variant === 'membership-subscribe' ? (
      <Iconify icon="ic:round-info" color="white" width={24} height={24} />
    ) : (
      <Iconify
        icon="icon-park-solid:caution"
        color="white"
        width={24}
        height={24}
      />
    );
  };

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: '1056px',
        height: '44px',
        display: 'flex',
        alignItems: 'center',
        backgroundColor:
          variant === 'upload-documents' ? '#FFC80033' : '#A2429529',
        justifyContent: 'space-between',
        position: 'absolute',
        top: 0,
        zIndex: 3000,
        left: { xs: '0px', sm: '-20px', md: '-20px', lg: '-40px' },
      }}
    >
      {/* Icon Box */}
      <Box
        sx={{
          width: '104px',
          height: '100%',
          backgroundColor:
            variant === 'upload-documents' ? '#FFC800' : '#A24295',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
        }}
      >
        {getIcon()}
      </Box>

      {/* Message */}
      <Box sx={{ flex: 1, ml: 2 }}>
        <Typography fontSize={16} fontWeight={500}>
          {getMessage()}
        </Typography>
      </Box>

      {/* CTA / Close Icon */}
      {variant === 'upload-documents' ? (
        <Box
          sx={{ ml: 2, cursor: 'pointer', mr: 2 }}
          onClick={() => router.push('/professional/document-verification')}
        >
          <Typography fontSize={16} fontWeight={600} sx={{ color: '#A24295' }}>
            Upload now
          </Typography>
        </Box>
      ) : (
        <IconButton sx={{ mr: 1 }} onClick={() => setVisible(false)}>
          <Iconify icon="mdi:close" width={20} height={20} color={'#A24295'} />
        </IconButton>
      )}
    </Box>
  );
};

export default VerificationBanner;
