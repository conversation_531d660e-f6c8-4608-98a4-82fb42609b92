'use client';

import { Box } from '@mui/material';
import ProfileIntro from './ProfileIntro';
import { BackButton } from '../../buttons/Backbutton';
import { useRouter } from 'next/navigation';
import { Profile } from '@minicardiac-client/types';
import EditButton from '../../buttons/EditButton';

interface Props {
  profile: Profile;
  ConnectUserComponent?: (props: { profile: Profile }) => JSX.Element;
  ProfileConnectionsComponent?: (props: { profile: Profile }) => JSX.Element;
  isOwner?: boolean;
}

export default function ProfileHeader({
  profile,
  ConnectUserComponent,
  ProfileConnectionsComponent,
  isOwner = false,
}: Props) {
  const router = useRouter();

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: '976px',
        height: 'auto',
        overflow: 'hidden',
        position: 'relative',
        backgroundColor: '#fff',
        borderRadius: '8px',
        pb: '20px',
      }}
    >
      {/* Cover image */}
      <Box
        sx={{
          height: '314px',
          width: '100%',
          position: 'relative',
          backgroundImage: profile.backgroundImage
            ? `url(${profile.backgroundImage})`
            : 'none',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          background: profile.backgroundImage ? 'transparent' : '#ffffff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          '&:hover .edit-cover-button': {
            display: 'flex',
            pointerEvents: 'auto',
          },
        }}
      >
        {/* Gradient overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background:
              'linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(124, 124, 124, 0.58),rgba(30, 30, 30, 1))',
          }}
        />

        {!profile.backgroundImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background:
                'linear-gradient(to bottom right, rgba(249, 34, 67, 0.3), rgba(162, 66, 149, 0.3))',
            }}
          />
        )}

        {/* Centered Icons if image is not present */}
        {!profile.backgroundImage && (
          <Box
            sx={{ display: 'flex', gap: 2, position: 'absolute', right: 50 }}
          >
            <Box
              component="img"
              src="/assets/user-profile/lifeline.svg"
              alt="Heart"
              sx={{
                width: {
                  xs: '172px',
                  smd: '100%',
                },
                height: 'auto',
              }}
            />
            <Box
              component="img"
              src="/assets/user-profile/heart.svg"
              alt="Heart"
              sx={{
                width: {
                  xs: '135px',
                  smd: '100%',
                },
                height: 'auto',
              }}
            />
          </Box>
        )}

        <BackButton
          sx={{ color: 'white', position: 'absolute', top: 16, left: 16 }}
          onClick={() => router.back()}
        />

        {ProfileConnectionsComponent ? (
          <ProfileConnectionsComponent profile={profile} />
        ) : (
          <Box sx={{ p: 2, color: 'gray' }}>Connections info not available</Box>
        )}

        {/* {isOwner && ( */}
          <EditButton label="Edit cover image" className="edit-cover-button" />
        {/* )} */}
      </Box>

      <ProfileIntro intro={profile.intro} />

      <Box sx={{ display: { xs: 'block', lg: 'none' } }}>
        {ConnectUserComponent ? (
          <ConnectUserComponent profile={profile} />
        ) : (
          <Box sx={{ p: 2, color: 'gray' }}>Connect option not available</Box>
        )}
      </Box>
    </Box>
  );
}
