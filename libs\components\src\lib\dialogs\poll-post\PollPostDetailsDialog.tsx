'use client';

import { Box, TextField } from '@mui/material';
import CustomToggleButtonGroup from '../../buttons/CustomToggleButtonGroup';
import DurationDropdown from './DurationDropdown';
import { PollPostDetailsDialogProps } from './types';
import { useTranslations } from 'next-intl';

const PollPostDetailsDialog = ({
  caption,
  setCaption,
  tags,
  setTags,
  audience,
  setAudience,
  speciality,
  setSpeciality,
  duration,
  setDuration,
}: PollPostDetailsDialogProps) => {
  const t = useTranslations('pollPost');

  return (
    <Box display="flex" flexDirection="column" flex={1}>
      <Box mt={{ xs: '20px', sm: '40px' }} mb="40px">
        <TextField
          placeholder={t('captionPlaceholder')}
          value={caption}
          onChange={(e) => setCaption(e.target.value)}
          fullWidth
          multiline
          minRows={3}
          InputLabelProps={{ shrink: true }}
          label={t('caption')}
          sx={{
            '& .MuiOutlinedInput-root': {
              height: { xs: 181, sm: 136 },
              alignItems: 'start',
              '& textarea': {
                height: '100% !important',
                boxSizing: 'border-box',
                resize: 'none',
                overflowY: 'auto',
                '&::placeholder': {
                  textAlign: 'left',
                  verticalAlign: 'top',
                  lineHeight: 1.2,
                },
              },
            },
          }}
        />
      </Box>

      <Box
        display="flex"
        flexDirection={{ xs: 'column' }}
        gap={{ xs: '20px', lg: '20px' }}
        alignItems={{ xs: 'center', md: 'end' }}
        mb="40px"
      >
        <TextField
          placeholder={t('tagsPlaceholder')}
          value={tags}
          onChange={(e) => setTags(e.target.value)}
          fullWidth
          label={t('tags')}
          InputLabelProps={{ shrink: true }}
        />
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: '20px',
            width: '100%',
          }}
        >
          <Box width={{ xs: '100%', md: '304px' }}>
            <CustomToggleButtonGroup
              label={t('audience')}
              options={['PROFESSIONAL', 'PUBLIC', 'BOTH']}
              selected={audience}
              onChange={setAudience}
              width={{ xs: '100%', md: '304px' }}
            />
          </Box>
          <Box width={{ xs: '100%', md: '304px' }}>
            <CustomToggleButtonGroup
              label={t('community')}
              options={['CARDIAC_SURGEON', 'CARDIOLOGIST', 'BOTH']}
              selected={speciality}
              onChange={setSpeciality}
              width={{ xs: '100%', md: '304px' }}
            />
          </Box>
        </Box>
      </Box>

      <Box mb="40px">
        <DurationDropdown duration={duration} setDuration={setDuration} />
      </Box>
    </Box>
  );
};

export default PollPostDetailsDialog;
