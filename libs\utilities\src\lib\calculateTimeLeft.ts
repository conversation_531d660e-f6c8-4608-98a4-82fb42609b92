import {
  addWeeks,
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  differenceInSeconds,
  isBefore,
  isAfter,
  parseISO,
} from 'date-fns';

export const calculateTimeLeft = (createdAtStr: string) => {
  const createdTime = parseISO(createdAtStr);
  const now = new Date();

  const oneWeekLater = addWeeks(createdTime, 1);
  const twoWeeksLater = addWeeks(createdTime, 2);

  if (isBefore(now, oneWeekLater)) {
    return { status: 'before', timeLeft: null };
  }

  if (isAfter(now, twoWeeksLater)) {
    return { status: 'after', timeLeft: null };
  }

  const days = differenceInDays(twoWeeksLater, now);
  const hours = differenceInHours(twoWeeksLater, now) % 24;
  const minutes = differenceInMinutes(twoWeeksLater, now) % 60;
  const seconds = differenceInSeconds(twoWeeksLater, now) % 60;

  return {
    status: 'during',
    timeLeft: { days, hours, minutes, seconds },
  };
};
