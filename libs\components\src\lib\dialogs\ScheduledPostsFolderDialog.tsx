// src/components/dialogs/DraftsFolderDialog.tsx
'use client';

import { Dialog, Box, Typography, IconButton } from '@mui/material';
import { Iconify } from '../iconify';
import { ScheduledPostCard } from './ScheduledPostsFolderCards';
import { sampleDrafts } from './samplePostData';

interface ScheduledPostsFolderDialogProps {
  open: boolean;
  onClose: () => void;
}

export default function ScheduledPostsFolderDialog({
  open,
  onClose,
}: ScheduledPostsFolderDialogProps) {
  const handleEdit = (id: string) => {
    console.log('Edit scheduled post:', id);
  };

  const handleDelete = (id: string) => {
    console.log('Delete scheduled post:', id);
  };

  const handleReschedule = (id: string) => {
    console.log('Reschedule post:', id);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          borderRadius: '12px',
          backgroundColor: 'white',
          p: 0,
        },
      }}
    >
      {/* Header */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        p={3}
        borderBottom="1px solid #eee"
        sx={{
          maxHeight: '70vh',
          overflowY: 'auto',
          '&::-webkit-scrollbar': { display: 'none' },
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 600,
            fontSize: '1.5rem',
            color: '#1E1E1E',
          }}
        >
          Scheduled
        </Typography>
        <IconButton onClick={onClose}>
          <Iconify
            icon="mingcute:close-line"
            width={36}
            height={36}
            color="#A24295"
          />
        </IconButton>
      </Box>
      <Box p={3}>
        {sampleDrafts.map((post) => (
          <ScheduledPostCard
            key={post.id}
            post={post}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onReschedule={handleReschedule}
          />
        ))}
      </Box>
    </Dialog>
  );
}
