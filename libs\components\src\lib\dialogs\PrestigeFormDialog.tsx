'use client';

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Ty<PERSON>graphy,
  Button,
  Box,
  TextField,
  MenuItem,
  Checkbox,
  FormControlLabel,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { usePostDialogStore } from '@minicardiac-client/apis';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';

interface PrestigeFormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

interface FormData {
  website: string;
  organisationSize: string;
  pointOfContactName: string;
  pointOfContactEmail: string;
  pointOfContactPhone: string;
  allowSubsidiaries: boolean;
}

const schema = yup.object().shape({
  website: yup
    .string()
    .url('Enter a valid URL')
    .required('Website is required'),
  organisationSize: yup.string().required('Organisation size is required'),
  pointOfContactName: yup.string().required('Name is required'),
  pointOfContactEmail: yup
    .string()
    .email('Enter a valid email')
    .required('Email is required'),
  pointOfContactPhone: yup.string().required('Phone number is required'),
  allowSubsidiaries: yup.boolean().default(false),
});

const PrestigeFormDialog = ({
  open,
  onClose,
  onSubmit,
}: PrestigeFormDialogProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { setActiveDialog } = usePostDialogStore();

  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      website: '',
      organisationSize: '',
      pointOfContactName: '',
      pointOfContactEmail: '',
      pointOfContactPhone: '',
      allowSubsidiaries: false,
    },
  });

  const onFormSubmit = (data: FormData) => {
    console.log('Form submitted:', data);
    setActiveDialog('PrestigeNote');
    onSubmit(data);
    reset();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: '728px',
          padding: '0',
          borderRadius: '12px',
          backgroundColor: '#fff',
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      <form onSubmit={handleSubmit(onFormSubmit)}>
        <DialogContent
          sx={{
            padding: '32px',
            paddingBottom: 0,
            flex: 1,
            gap: '40px',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
              alignItems: 'center',
            }}
          >
            <Typography fontSize="28px" fontWeight={500}>
              Prestige Account Application
            </Typography>
            <Typography fontSize="16px" fontWeight={400} color="#737678">
              Please fill in the details below to help apply for your Prestige
              Membership Plan
            </Typography>
          </Box>

          <Box display="flex" flexDirection="column" gap={2}>
            <Box
              display="flex"
              gap={2}
              flexDirection={isMobile ? 'column' : 'row'}
            >
              <Controller
                name="organisationSize"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    select
                    label="Organisation Size (employees)"
                    fullWidth
                    error={!!errors.organisationSize}
                    helperText={errors.organisationSize?.message}
                    sx={{
                      maxWidth: '320px',
                      width: '100%',
                    }}
                  >
                    <MenuItem value="1-10">Between 1–10</MenuItem>
                    <MenuItem value="11-50">Between 11–50</MenuItem>
                    <MenuItem value="51-100">Between 50–100</MenuItem>
                    <MenuItem value="100+">More than 100</MenuItem>
                  </TextField>
                )}
              />

              <Controller
                name="website"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Organisation Website"
                    placeholder="https:// - Enter the link to your website"
                    fullWidth
                    error={!!errors.website}
                    helperText={errors.website?.message}
                    sx={{
                      maxWidth: '320px',
                      width: '100%',
                    }}
                  />
                )}
              />
            </Box>

            <Box
              display="flex"
              gap={2}
              flexDirection={isMobile ? 'column' : 'row'}
            >
              <Controller
                name="pointOfContactName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Name of point of contact"
                    placeholder="Name of the point of contact"
                    fullWidth
                    error={!!errors.pointOfContactName}
                    helperText={errors.pointOfContactName?.message}
                    sx={{
                      maxWidth: '320px',
                      width: '100%',
                    }}
                  />
                )}
              />
              <Controller
                name="pointOfContactEmail"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="E-mail of point of contact"
                    placeholder="E-mail of the point of contact"
                    fullWidth
                    error={!!errors.pointOfContactEmail}
                    helperText={errors.pointOfContactEmail?.message}
                    sx={{
                      maxWidth: '320px',
                      width: '100%',
                    }}
                  />
                )}
              />
            </Box>

            <Controller
              name="pointOfContactPhone"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Number of point of contact"
                  placeholder="Phone number of point of contact "
                  error={!!errors.pointOfContactPhone}
                  helperText={errors.pointOfContactPhone?.message}
                  sx={{
                    maxWidth: '320px',
                    width: '100%',
                  }}
                />
              )}
            />

            <Controller
              name="allowSubsidiaries"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={<Checkbox {...field} checked={field.value} />}
                  label="I would like to add Subsidiary Organisation accounts under this Prestige plan"
                  sx={{
                    fontSize: '16px',
                    fontWeight: 400,
                  }}
                />
              )}
            />
          </Box>
        </DialogContent>

        <Box
          sx={{
            padding: '20px',
            borderTop: '1px solid #eee',
          }}
        >
          <Box display="flex" justifyContent="center">
            <Button
              variant="contained"
              type="submit"
              sx={{
                width: '137px',
                backgroundColor: '#A24295',
              }}
            >
              Submit
            </Button>
          </Box>
        </Box>
      </form>
    </Dialog>
  );
};

export default PrestigeFormDialog;
