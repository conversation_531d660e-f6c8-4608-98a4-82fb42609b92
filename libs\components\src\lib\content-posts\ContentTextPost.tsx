'use client';

import { useState, useRef, useEffect, forwardRef } from 'react';
import { Box, Divider, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';

import PostFooterActions from './PostFooterActions';
import PostHeader from './PostHeader';
import { FullPageLoader } from '../full-page-loader';
import HighlightText from '../common/HighlightText';
import { HighlightHtml } from '../common/HighlightHtml';
import { useFeedSearchStore } from '../store/useFeedSearchStore';
import { generateSlug } from '@minicardiac-client/utilities';

interface UserInfo {
  name: string;
  profilePic: string;
  postedAgo: string;
}

interface TextPostProps {
  id?: string;
  title?: string;
  user?: UserInfo;
  content?: string;
  likes?: number;
  isLiked?: boolean;
  comments?: number;
  reposts?: number;
  shares?: number;
  postId?: string;
  ghost?: boolean;
  post?: {
    tags?: string[];
  };
}

const MAX_LINES = 6;

const ContentTextPost = forwardRef(function ContextTextPost(
  {
    id = '7338905434222669825',
    title = 'rethinking-our-approach-to-hfpef',
    user = {
      name: 'Roger Taylor',
      profilePic: '/placeholder-avatar.png',
      postedAgo: 'just now',
    },
    content = `
<p style="font-size:16px; font-weight:500; margin-bottom:16px;">
  Rethinking Our Approach to HFpEF
</p>

<p style="font-size:12px; font-weight:400; margin-bottom:16px;">
  Lately, I’ve been reflecting on the evolving landscape of heart failure with preserved ejection fraction (HFpEF). Despite increasing recognition and clinical focus, we still lack robust, targeted therapies that consistently improve long-term outcomes.
</p>

<p style="font-size:12px; font-weight:400; margin-bottom:16px;">
  With more patients—especially older adults and women—being diagnosed, are we doing enough to stratify risk early and personalize management strategies? The recent guidelines emphasize comorbidity optimization, but we need more large-scale trials to refine treatment algorithms.
</p>

<p style="font-size:12px; font-weight:400; margin-bottom:16px;">
  Curious to hear how others are managing these cases in real-world practice. Are you integrating newer agents like SGLT2 inhibitors routinely? How do you handle the diagnostic gray zones?
</p>

<p style="font-size:12px; font-weight:700;">
  Let’s keep the conversation going—we learn the most from each other. #Cardiology #HeartFailure #HFpEF #ClinicalPractice #CardioCommunity
</p>`,
    likes = 600,
    isLiked = false,
    comments = 25,
    reposts = 12,
    shares = 5,
    postId,
    ghost = false,
    ...eventHandlers
  }: TextPostProps & React.HTMLAttributes<HTMLDivElement>,
  ref: React.Ref<HTMLDivElement>
) {
  const [showMore, setShowMore] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showSeeMore, setShowSeeMore] = useState(false);
  const [showComments, setShowComments] = useState(false);

  const contentRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const searchKeyword = useFeedSearchStore((state) => state.searchKeyword);

  const handleNavigate = () => {
    setLoading(true);

    const slug = generateSlug(content);

    setLoading(true);
    router.push(`/feed/text/${id}/${slug}`);
  };

  const handleCommentClick = () => {
    setShowComments((prev) => !prev);
  };

  useEffect(() => {
    if (contentRef.current) {
      const lineHeight = 18; // must match Typography's line height
      const maxHeight = MAX_LINES * lineHeight;
      if (contentRef.current.scrollHeight > maxHeight) {
        setShowSeeMore(true);
      }
    }
  }, []);

  return (
    <>
      <FullPageLoader
        open={loading}
        message="Loading post..."
        sx={{ backgroundColor: '#1E1E1E40' }}
      />
      <Box
        ref={ref}
        {...eventHandlers}
        sx={{
          width: '100%',
          p: '20px',
          borderRadius: '12px',
          backgroundColor: '#fff',
          boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
          boxSizing: 'border-box',
          cursor: eventHandlers.onMouseDown ? 'grab' : 'default',
        }}
      >
        <PostHeader user={user} showOptions={true} />

        {/* Title highlight if available */}
        {title && (
          <Typography
            sx={{
              fontSize: '16px',
              fontWeight: 700,
              color: '#1E1E1E',
              lineHeight: '22px',
              mb: 1,
            }}
          >
            <HighlightText text={title} keyword={searchKeyword} />
          </Typography>
        )}

        <Box
          mt="16px"
          sx={{ position: 'relative', width: '100%' }}
          onClick={handleNavigate}
        >
          {/* If content is plain text, highlight; if HTML, use HighlightHtml */}
          {content && !content.trim().startsWith('<') ? (
            <Typography
              component="div"
              sx={{
                fontSize: '14px',
                lineHeight: '18px',
                fontWeight: 400,
                color: '#1E1E1E',
                display: '-webkit-box',
                WebkitLineClamp: showMore ? 'unset' : MAX_LINES,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                userSelect: 'text',
              }}
            >
              <HighlightText text={content} keyword={searchKeyword} />
            </Typography>
          ) : (
            <Typography
              component="div"
              sx={{
                fontSize: '14px',
                lineHeight: '18px',
                fontWeight: 400,
                color: '#1E1E1E',
                display: '-webkit-box',
                WebkitLineClamp: showMore ? 'unset' : MAX_LINES,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                userSelect: 'text',
              }}
            >
              <HighlightHtml html={content} keyword={searchKeyword} />
            </Typography>
          )}

          {!showMore && showSeeMore && (
            <Typography
              sx={{
                mt: '4px',
                fontSize: '14px',
                fontWeight: 500,
                color: '#A24295',
                cursor: 'pointer',
                userSelect: 'none',
                display: 'inline-block',
              }}
              onClick={(e) => {
                e.stopPropagation(); // Prevent navigation
                setShowMore(true);
              }}
            >
              See more
            </Typography>
          )}
        </Box>

        <Divider
          sx={{ mt: '12px', mb: '8px', borderColor: '#A3A3A3', opacity: 0.5 }}
        />

        <PostFooterActions
          likes={likes}
          isLiked={isLiked}
          commentsCount={comments}
          reposts={reposts}
          shares={shares}
          onOpenComments={handleCommentClick}
          showComments={showComments}
          setShowComments={setShowComments}
          postId={postId}
        />
      </Box>
    </>
  );
});

export default ContentTextPost;
