// components/sidebar/RightSidebar.tsx
'use client';
import React from 'react';
import { Box, SxProps } from '@mui/material';
import RightSidebarTags from '../tags/RightSidebarTags';
import RightSidebarFolders from './RightSidebarFolders';

interface RightSidebarProps {
  variant?: 'tags' | 'folders';
  sx?: SxProps;
}

const RightSidebar: React.FC<RightSidebarProps> = ({
  variant = 'tags',
  sx,
}) => {
  return (
    <Box
      sx={{
        display: { xs: 'none', lg: 'flex' },
        flexDirection: 'column',
        position: 'relative',
        ...sx,
      }}
    >
      {variant === 'tags' ? <RightSidebarTags /> : <RightSidebarFolders />}
    </Box>
  );
};

export default RightSidebar;
