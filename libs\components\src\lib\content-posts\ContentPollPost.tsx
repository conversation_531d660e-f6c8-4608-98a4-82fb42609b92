'use client';

import { forwardRef, useState } from 'react';
import { Box } from '@mui/material';
import PostFooterActions from '../content-posts/PostFooterActions';
import PostHeader from '../content-posts/PostHeader';
import { PollCaption } from '../poll-post/PollCaption';
import { FeedPostType } from '@minicardiac-client/types';
import MainPollContent from '../poll-post/MainPollContent';

const ContentPollPost = forwardRef(function ContentPollPost(
  {
    postId,
    ghost = false,
    post,
    ...eventHandlers
  }: {
    postId?: string;
    ghost?: boolean;
    post?: FeedPostType;
  } & React.HTMLAttributes<HTMLDivElement>,
  ref: React.Ref<HTMLDivElement>
) {
  const [showComments, setShowComments] = useState(false);

  const handleCommentClick = () => {
    setShowComments((prev) => !prev);
  };

  return (
    <Box
      ref={ref}
      sx={{
        width: '100%',
        p: { xs: '16px', sm: '20px' },
        borderRadius: '8px',
        backgroundColor: '#fff',
        boxShadow: '0px 1px 6px rgba(0, 0, 0, 0.05)',
        boxSizing: 'border-box',
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
        cursor: eventHandlers.onMouseDown ? 'grab' : 'default',
      }}
      {...eventHandlers}
    >
      <PostHeader
        user={{
          name: post?.publisherName || '',
          profilePic: post?.profileImageUrlThumbnail || '',
          postedAgo: post?.postedAt || '',
        }}
        showOptions
      />

      <PollCaption POLL_CAPTION={post?.content || ''} postId={post?.id || ''} />
      <MainPollContent
        question={post?.question}
        options={post?.options}
        allowCustomAnswer={post?.allowCustomAnswer || false}
        totalVotes={post?.totalVotes || 0}
        postId={post?.id || ''}
        expiresAt={post?.expiresAt || ''}
      />

      <PostFooterActions
        likes={post?.likesCount ?? 0}
        isLiked={post?.isLiked ?? false}
        commentsCount={post?.commentsCount ?? 0}
        reposts={post?.repostCount ?? 0}
        shares={post?.shareCount ?? 0}
        onOpenComments={handleCommentClick}
        showComments={showComments}
        setShowComments={setShowComments}
        postId={post?.id || ''}
      />
    </Box>
  );
});

export default ContentPollPost;
