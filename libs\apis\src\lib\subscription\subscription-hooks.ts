import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { axiosInstance } from '../http-client.js';
import { SubscriptionOption } from '@minicardiac-client/types';
import { auth } from '../firebase/firebase-client.js';

// Cache for subscription plans (in-memory cache with 1 hour TTL)
const subscriptionPlansCache: {
  [key: string]: {
    data: SubscriptionOption[];
    timestamp: number;
  };
} = {};

const CACHE_TTL_MS = 60 * 60 * 1000; // 1 hour cache TTL

// Define query keys for subscription plans
export const subscriptionQueryKeys = {
  all: ['subscription'] as const,
  plans: (userSegment?: string) =>
    [...subscriptionQueryKeys.all, 'plans', userSegment] as const,
};

// Define subscription request type
export interface CreateSubscriptionRequest {
  subscriptionPlanId: string;
  isYearly: boolean;
  professionalCategory?: string;
  prestigeData?: any;
}

// Define subscription response type
export interface CreateSubscriptionResponse {
  id: string;
  planId: number;
  status: string;
  // Add other fields as needed
}

// API function to fetch subscription plans with retry and cache
export const fetchSubscriptionPlans = async (
  userSegment: string
): Promise<SubscriptionOption[]> => {
  // Check cache first
  const cached = subscriptionPlansCache[userSegment];
  const now = Date.now();

  if (cached && now - cached.timestamp < CACHE_TTL_MS) {
    console.log('Returning cached subscription plans');
    return cached.data;
  }

  // Retry configuration
  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await axiosInstance.get(
        `/subscription-plans/${userSegment}`
      );
      const data = response.data.data || [];

      // Update cache
      subscriptionPlansCache[userSegment] = {
        data,
        timestamp: now,
      };

      return data;
    } catch (error: unknown) {
      const errorObj = error as { response?: { status?: number } };
      lastError = error instanceof Error ? error : new Error(String(error));

      // Don't retry on 403 after first attempt to prevent infinite loops
      if (errorObj?.response?.status === 403 && attempt > 1) {
        console.warn('Access forbidden after retry, giving up');
        break;
      }

      // Exponential backoff: 1s, 2s, 4s
      const delayMs = 1000 * Math.pow(2, attempt - 1);
      console.warn(
        `Attempt ${attempt} failed, retrying in ${delayMs}ms...`,
        error
      );

      if (attempt < maxRetries) {
        await new Promise((resolve) => setTimeout(resolve, delayMs));
      }
    }
  }

  // If we have stale cache data, return it as fallback
  if (cached) {
    console.warn('Using stale cache data due to API failure');
    return cached.data;
  }

  // If no cache and all retries failed, throw the last error
  throw lastError || new Error('Failed to fetch subscription plans');
};

// API function to create a subscription
export const createSubscription = async (
  data: CreateSubscriptionRequest
): Promise<CreateSubscriptionResponse> => {
  console.log('Creating subscription with data:', data);
  try {
    const response = await axiosInstance.post('/subscription-plans', data);
    console.log('Create subscription response:', response.data);

    // After successful subscription, refresh the Firebase token to get updated claims
    if (auth?.currentUser) {
      console.log('Refreshing Firebase token to get updated claims...');
      await auth.currentUser.getIdToken(true);
      console.log('Firebase token refreshed successfully');
    }

    return response.data.data || response.data;
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error
        ? error.message
        : 'Unknown error creating subscription';
    console.error('Error creating subscription:', errorMessage);

    if ((error as { response: any }).response) {
      console.error('Error response:', (error as { response: any }).response);
    } else if ((error as { request?: any }).request) {
      console.error(
        'No response received:',
        (error as { request: any }).request
      );
    } else {
      console.error('Error setting up request:', errorMessage);
    }

    // Extract and log detailed error information
    const errorObj = error as {
      response?: { data?: any; status?: number; headers?: any };
    };
    if (errorObj?.response) {
      console.error('Error response data:', errorObj.response.data);
      console.error('Error status:', errorObj.response.status);
      console.error('Error headers:', errorObj.response.headers);
    }

    throw error;
  }
};

// Hook to get subscription plans
export const useGetSubscriptionPlans = (userSegment = 'CARDIAC_SPECIALIST') => {
  return useQuery({
    queryKey: subscriptionQueryKeys.plans(userSegment),
    queryFn: () => fetchSubscriptionPlans(userSegment),
    staleTime: CACHE_TTL_MS, // Consider data fresh for cache TTL duration
    gcTime: CACHE_TTL_MS * 2, // Keep unused data in cache for 2x TTL
    retry: 2, // Retry failed requests twice
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });
};

// Hook to create a subscription
export const useCreateSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createSubscription,
    onSuccess: () => {
      // Invalidate relevant queries after successful subscription
      queryClient.invalidateQueries({ queryKey: subscriptionQueryKeys.all });
    },
  });
};
